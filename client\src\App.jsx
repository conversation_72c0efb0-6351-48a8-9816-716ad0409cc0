import React, { useState, useRef, useEffect } from 'react'
import VoiceRecorder from './components/VoiceRecorder'
import NotesList from './components/NotesList'

function App() {
  const [notes, setNotes] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)

  // Load notes from both localStorage and server on mount
  useEffect(() => {
    loadNotes()
  }, [])

  // Save notes to localStorage whenever notes change
  useEffect(() => {
    if (!isLoading) {
      localStorage.setItem('vocable-notes', JSON.stringify(notes))
    }
  }, [notes, isLoading])

  const loadNotes = async () => {
    try {
      setIsLoading(true)
      setError(null)

      // First, load from localStorage for immediate display
      const savedNotes = localStorage.getItem('vocable-notes')
      if (savedNotes) {
        setNotes(JSON.parse(savedNotes))
      }

      // Then, fetch from server to get the latest data
      const response = await fetch('/api/summarizations')
      if (response.ok) {
        const data = await response.json()
        const serverNotes = data.summaries.map(summary => ({
          id: summary.id,
          timestamp: summary.createdAt,
          duration: Math.floor(Math.random() * 120) + 10, // Placeholder since we don't store duration
          summary: summary.summary,
          promptType: summary.promptType,
          audioUrl: summary.audioUrl,
          serverId: summary.id,
          metadata: summary.metadata
        }))

        // Merge with local notes, preferring server data for existing items
        const localNotes = savedNotes ? JSON.parse(savedNotes) : []
        const mergedNotes = mergeNotes(localNotes, serverNotes)
        setNotes(mergedNotes)
      } else {
        console.warn('Failed to fetch notes from server, using local storage only')
      }
    } catch (error) {
      console.error('Error loading notes:', error)
      setError('Failed to load some notes from server')
    } finally {
      setIsLoading(false)
    }
  }

  const mergeNotes = (localNotes, serverNotes) => {
    const serverIds = new Set(serverNotes.map(note => note.serverId))
    const localOnlyNotes = localNotes.filter(note => !note.serverId || !serverIds.has(note.serverId))

    // Combine server notes with local-only notes, sort by timestamp
    const allNotes = [...serverNotes, ...localOnlyNotes]
    return allNotes.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
  }

  const addNote = (noteData) => {
    const newNote = {
      id: noteData.id || Date.now(),
      timestamp: noteData.createdAt || new Date().toISOString(),
      serverId: noteData.id, // Store server ID for syncing
      ...noteData
    }
    setNotes(prev => [newNote, ...prev])
  }

  const deleteNote = async (noteId) => {
    try {
      const note = notes.find(n => n.id === noteId)
      if (note && note.serverId) {
        // Delete from server if it has a server ID
        const response = await fetch(`/api/summarizations/${note.serverId}`, {
          method: 'DELETE'
        })
        if (!response.ok) {
          console.warn('Failed to delete note from server')
        }
      }

      // Remove from local state
      setNotes(prev => prev.filter(n => n.id !== noteId))
    } catch (error) {
      console.error('Error deleting note:', error)
      // Still remove from local state even if server deletion fails
      setNotes(prev => prev.filter(n => n.id !== noteId))
    }
  }

  return (
    <div className="container">
      <div className="header">
        <h1>Vocable</h1>
        <p>Record voice notes and get AI-powered summaries</p>
        {error && (
          <div className="error-banner">
            {error}
          </div>
        )}
      </div>

      <VoiceRecorder onNoteCreated={addNote} />
      <NotesList
        notes={notes}
        isLoading={isLoading}
        onDeleteNote={deleteNote}
        onRefresh={loadNotes}
      />
    </div>
  )
}

export default App