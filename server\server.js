import express from 'express'
import multer from 'multer'
import cors from 'cors'
import { GoogleGenerativeAI } from '@google/generative-ai'
import dotenv from 'dotenv'
import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

dotenv.config()

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

const app = express()
const PORT = process.env.PORT || 3001

// Middleware
app.use(cors())
app.use(express.json())

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(__dirname, 'uploads')
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true })
}

// Create summaries directory for storing summarization data
const summariesDir = path.join(__dirname, 'summaries')
if (!fs.existsSync(summariesDir)) {
  fs.mkdirSync(summariesDir, { recursive: true })
}

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadsDir)
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9)
    cb(null, `recording-${uniqueSuffix}.webm`)
  }
})

const upload = multer({ 
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  }
})

// Initialize Gemini AI
let genAI
if (process.env.GEMINI_API_KEY) {
  genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY)
} else {
  console.warn('GEMINI_API_KEY not found in environment variables')
}

// Helper function to convert file to Gemini format
function fileToGenerativePart(filePath, mimeType) {
  return {
    inlineData: {
      data: fs.readFileSync(filePath).toString('base64'),
      mimeType
    }
  }
}

// Predefined prompt templates
const PROMPT_TEMPLATES = {
  general: ` This is a voice summarization task. Please analyze this audio recording and provide a concise, helpful summary. 
  Focus on:
    - Key topics discussed
    - Important points or decisions
    - Action items if any
    - Overall context and purpose

    DO NOT use personal pronouns or say "The speaker said..." or other similar things. You should only state the objective things that were said, with reference to who said it. 

    The speaker in the audio is the user. Do not provide a desctiption of what has happened as if the user is different from the speaker.

    You goal is to provide something that the user can read back at a later time to hear their own thoughts but in a summarized format.

    You should not include any additional commentary or analysis beyond what was said in the recording.

    You should avoid adding your own opinions or interpretations.

    You should generate the summary without referencing the speaker. Instead, you should provide a summary that user will read and come back to reference later

    Keep the summary clear and under 200 words. If the audio is unclear or contains no meaningful content, please indicate that.`,
  
  meeting: `Analyze this meeting recording and provide a structured summary:
    
    Meeting Summary:
    - Main topics discussed
    - Key decisions made
    - Action items and who's responsible
    - Next steps or follow-ups needed
    
    Key Points:
    - Important details or insights
    - Any concerns or issues raised
    
    Format as a clear, professional meeting summary under 250 words.

    You should not include any additional commentary or analysis beyond what was said in the recording.

    You should avoid adding your own opinions or interpretations.

    You should generate the summary without referencing the speaker. Instead, you should provide a summary that user will read and come back to reference later

    Keep the summary clear and under 200 words. If the audio is unclear or contains no meaningful content, please indicate that
    
    `,
  
  brainstorm: `Analyze this brainstorming session and extract:
    
    Ideas Generated:
    - List the main ideas or concepts discussed
    - Note any creative solutions or approaches
    
    Key Insights:
    - Interesting connections or patterns
    - Potential next steps for development
    
    Action Items:
    - What needs to be researched or explored further
    
    Keep it organized and under 200 words.`,
  
  lecture: `Analyze this educational content and provide:

    Main Topics:
    - Key concepts covered
    - Important definitions or explanations
    
    Key Takeaways:
    - Most important points to remember
    - Practical applications mentioned
    
    Study Notes:
    - Facts, figures, or examples given
    - Any questions or areas for further study
    
    Format as clear study notes under 250 words.`,
  
  personal: `
    
    You are simply providing a summary of what they have recorded. Do not analyze it or reference them or yourself in any way. 
    
    Do not provide a description of what happened, rather provide a candid response that the speaker can look back at and read in their own perosnal voice. 

    You are essentially condensing their thoughts. You are not analyzing it as a AI model, but rather as a person who is simply writing down what they said. 

    You should NOT be providing a 3rd-person descrirption of what has happened, instead, think of yourself as the person who said the things in the audio file and just condense it in their own voice. 
    `,
  
  interview: `Analyze this interview recording and provide:
    
    Interview Summary:
    - Key questions asked and responses
    - Important qualifications or experience mentioned
    - Candidate's main strengths highlighted
    
    Notable Points:
    - Interesting insights or unique perspectives
    - Any concerns or red flags
    
    Next Steps:
    - Follow-up questions or areas to explore
    
    Format as a professional interview summary under 250 words.`
}

// API Routes
app.post('/api/process-audio', upload.single('audio'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No audio file provided' })
    }

    if (!genAI) {
      return res.status(500).json({ 
        error: 'Gemini API not configured. Please set GEMINI_API_KEY environment variable.' 
      })
    }

    const filePath = req.file.path
    const { promptType = 'general', customPrompt } = req.body
    
    console.log('Processing audio file:', filePath)
    console.log('Prompt type:', promptType)

    // Get the generative model
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' })

    // Convert audio file to format Gemini can process
    const audioPart = fileToGenerativePart(filePath, 'audio/webm')

    // Use custom prompt if provided, otherwise use template
    let prompt
    if (customPrompt && customPrompt.trim()) {
      prompt = customPrompt.trim()
    } else if (PROMPT_TEMPLATES[promptType]) {
      prompt = PROMPT_TEMPLATES[promptType]
    } else {
      prompt = PROMPT_TEMPLATES.general
    }

    console.log('Using prompt type:', promptType)

    // Generate summary using Gemini
    const processingStartTime = Date.now()
    const result = await model.generateContent([prompt, audioPart])
    const response = await result.response
    const summary = response.text()
    const processingTime = Date.now() - processingStartTime

    // Create summarization data with metadata
    const summarizationId = `${Date.now()}-${Math.round(Math.random() * 1E9)}`
    const summarizationData = {
      id: summarizationId,
      summary: summary.trim(),
      promptType: promptType,
      customPrompt: customPrompt && customPrompt.trim() ? customPrompt.trim() : null,
      audioFilename: req.file.filename,
      audioUrl: `/uploads/${req.file.filename}`,
      audioSize: req.file.size,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      metadata: {
        originalFilename: req.file.originalname,
        mimeType: req.file.mimetype,
        processingTime: processingTime,
        geminiModel: 'gemini-1.5-flash'
      }
    }

    // Save summarization to local storage
    const saved = saveSummarization(summarizationData)
    if (!saved) {
      console.warn('Failed to save summarization to local storage')
    }

    // Clean up the uploaded file (optional - you might want to keep it)
    // fs.unlinkSync(filePath)

    res.json({
      id: summarizationId,
      summary: summary.trim(),
      promptType: promptType,
      audioUrl: `/uploads/${req.file.filename}`,
      createdAt: summarizationData.createdAt
    })

  } catch (error) {
    console.error('Error processing audio:', error)
    
    // Clean up file if it exists
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path)
    }

    res.status(500).json({ 
      error: 'Failed to process audio file',
      details: error.message 
    })
  }
})

// Get available prompt templates
app.get('/api/prompt-templates', (req, res) => {
  const templates = Object.keys(PROMPT_TEMPLATES).map(key => ({
    id: key,
    name: key.charAt(0).toUpperCase() + key.slice(1),
    description: getPromptDescription(key)
  }))
  
  res.json({ templates })
})

function getPromptDescription(promptType) {
  const descriptions = {
    general: 'General purpose summary with key topics and action items',
    meeting: 'Structured meeting notes with decisions and action items',
    brainstorm: 'Creative session summary with ideas and insights',
    lecture: 'Educational content with key concepts and study notes',
    personal: 'Personal voice notes with thoughts and reminders',
    interview: 'Interview summary with candidate evaluation'
  }
  return descriptions[promptType] || 'Custom prompt template'
}

// Helper functions for managing summarizations
function saveSummarization(summarizationData) {
  try {
    const filename = `summary-${summarizationData.id}.json`
    const filepath = path.join(summariesDir, filename)
    fs.writeFileSync(filepath, JSON.stringify(summarizationData, null, 2))
    return true
  } catch (error) {
    console.error('Error saving summarization:', error)
    return false
  }
}

function loadSummarization(id) {
  try {
    const filename = `summary-${id}.json`
    const filepath = path.join(summariesDir, filename)
    if (fs.existsSync(filepath)) {
      const data = fs.readFileSync(filepath, 'utf8')
      return JSON.parse(data)
    }
    return null
  } catch (error) {
    console.error('Error loading summarization:', error)
    return null
  }
}

function getAllSummarizations() {
  try {
    const files = fs.readdirSync(summariesDir)
    const summaries = []

    for (const file of files) {
      if (file.startsWith('summary-') && file.endsWith('.json')) {
        const filepath = path.join(summariesDir, file)
        try {
          const data = fs.readFileSync(filepath, 'utf8')
          const summary = JSON.parse(data)
          summaries.push(summary)
        } catch (error) {
          console.error(`Error reading summary file ${file}:`, error)
        }
      }
    }

    // Sort by creation date, newest first
    return summaries.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
  } catch (error) {
    console.error('Error loading all summarizations:', error)
    return []
  }
}

function deleteSummarization(id) {
  try {
    const filename = `summary-${id}.json`
    const filepath = path.join(summariesDir, filename)
    if (fs.existsSync(filepath)) {
      fs.unlinkSync(filepath)
      return true
    }
    return false
  } catch (error) {
    console.error('Error deleting summarization:', error)
    return false
  }
}

// API endpoints for managing summarizations
app.get('/api/summarizations', (req, res) => {
  try {
    const summaries = getAllSummarizations()
    res.json({ summaries })
  } catch (error) {
    console.error('Error fetching summarizations:', error)
    res.status(500).json({ error: 'Failed to fetch summarizations' })
  }
})

app.get('/api/summarizations/:id', (req, res) => {
  try {
    const { id } = req.params
    const summary = loadSummarization(id)

    if (!summary) {
      return res.status(404).json({ error: 'Summarization not found' })
    }

    res.json(summary)
  } catch (error) {
    console.error('Error fetching summarization:', error)
    res.status(500).json({ error: 'Failed to fetch summarization' })
  }
})

app.delete('/api/summarizations/:id', (req, res) => {
  try {
    const { id } = req.params
    const deleted = deleteSummarization(id)

    if (!deleted) {
      return res.status(404).json({ error: 'Summarization not found' })
    }

    res.json({ message: 'Summarization deleted successfully' })
  } catch (error) {
    console.error('Error deleting summarization:', error)
    res.status(500).json({ error: 'Failed to delete summarization' })
  }
})

// Serve uploaded files (optional)
app.use('/uploads', express.static(uploadsDir))

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    geminiConfigured: !!process.env.GEMINI_API_KEY
  })
})

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`)
  console.log(`Gemini API configured: ${!!process.env.GEMINI_API_KEY}`)
})