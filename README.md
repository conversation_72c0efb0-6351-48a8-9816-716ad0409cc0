# Vocable: Get better voicenotes

Hey! My name's <PERSON><PERSON><PERSON>. I built this cause I could find any good AI voice note apps (or at least I didn't try hard enough.)

Was bored and built this. UI is really bad right now, I'm a design nut, so gonna be replacing the react UI kit with a tilaored, beautiful UI soon. 

(If you're reading this rn, you're name's either <PERSON><PERSON><PERSON> or <PERSON>, so I didn't really think about documenting this insanely well.)

# Steps to use

Get your free Google Gemini api key from: https://aistudio.google.com/apikey
Add it to the .env file for the GEMINI_API_KEY variable.
run `npm install` in the terminal in the root directory of the project.
run `npm run dev`

use the app and give feedback pls!!