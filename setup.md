# Quick Setup Guide

## 🚀 Get Started in 3 Steps

### 1. Get Your Gemini API Key
1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Copy it for the next step

### 2. Configure Environment
```bash
# Copy the environment template
cp server/.env.example server/.env

# Edit server/.env and add your API key:
# GEMINI_API_KEY=your_actual_api_key_here
```

### 3. Start the App
```bash
npm run dev
```

That's it! The app will open at http://localhost:3000

## 🎤 How to Use

1. **Click the microphone** to start recording
2. **Click the stop button** when done
3. **Wait a moment** for AI processing
4. **View your summary** in the notes list below

## 🔧 Troubleshooting

**Microphone not working?**
- Check browser permissions
- Make sure you're on HTTPS or localhost

**API errors?**
- Verify your Gemini API key is correct
- Check the server console for error messages

**Build issues?**
- Try deleting node_modules and running `npm run install:all` again