import React, { useState, useRef, useEffect } from 'react'

const VoiceRecorder = ({ onNoteCreated }) => {
  const [isRecording, setIsRecording] = useState(false)
  const [recordingTime, setRecordingTime] = useState(0)
  const [isProcessing, setIsProcessing] = useState(false)
  const [promptType, setPromptType] = useState('general')
  const [customPrompt, setCustomPrompt] = useState('')
  const [showCustomPrompt, setShowCustomPrompt] = useState(false)
  const [promptTemplates, setPromptTemplates] = useState([])
  
  const mediaRecorderRef = useRef(null)
  const streamRef = useRef(null)
  const chunksRef = useRef([])
  const timerRef = useRef(null)

  useEffect(() => {
    // Load prompt templates on mount
    fetchPromptTemplates()
    
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current)
      }
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop())
      }
    }
  }, [])

  const fetchPromptTemplates = async () => {
    try {
      const response = await fetch('/api/prompt-templates')
      if (response.ok) {
        const data = await response.json()
        setPromptTemplates(data.templates)
      }
    } catch (error) {
      console.error('Error fetching prompt templates:', error)
    }
  }

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          sampleRate: 44100
        } 
      })
      
      streamRef.current = stream
      chunksRef.current = []
      
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      })
      
      mediaRecorderRef.current = mediaRecorder
      
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunksRef.current.push(event.data)
        }
      }
      
      mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(chunksRef.current, { type: 'audio/webm' })
        await processAudioFile(audioBlob)
        
        // Clean up
        streamRef.current.getTracks().forEach(track => track.stop())
        streamRef.current = null
      }
      
      mediaRecorder.start(1000) // Collect data every second
      setIsRecording(true)
      setRecordingTime(0)
      
      // Start timer
      timerRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1)
      }, 1000)
      
    } catch (error) {
      console.error('Error starting recording:', error)
      alert('Could not access microphone. Please check permissions.')
    }
  }

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop()
      setIsRecording(false)
      setIsProcessing(true)
      
      if (timerRef.current) {
        clearInterval(timerRef.current)
        timerRef.current = null
      }
    }
  }

  const processAudioFile = async (audioBlob) => {
    try {
      const formData = new FormData()
      formData.append('audio', audioBlob, 'recording.webm')
      formData.append('promptType', promptType)
      if (showCustomPrompt && customPrompt.trim()) {
        formData.append('customPrompt', customPrompt.trim())
      }
      
      const response = await fetch('/api/process-audio', {
        method: 'POST',
        body: formData
      })
      
      if (!response.ok) {
        throw new Error('Failed to process audio')
      }
      
      const result = await response.json()

      onNoteCreated({
        id: result.id,
        duration: recordingTime,
        summary: result.summary,
        promptType: result.promptType,
        audioUrl: result.audioUrl,
        createdAt: result.createdAt
      })
      
    } catch (error) {
      console.error('Error processing audio:', error)
      onNoteCreated({
        duration: recordingTime,
        summary: 'Error processing audio. Please try again.',
        promptType: promptType,
        error: true
      })
    } finally {
      setIsProcessing(false)
      setRecordingTime(0)
    }
  }

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  const handleRecordClick = () => {
    if (isRecording) {
      stopRecording()
    } else {
      startRecording()
    }
  }

  return (
    <div className="recording-section">
      {/* Prompt Selection */}
      <div className="prompt-selection">
        <h3>Choose Summary Style</h3>
        <div className="prompt-options">
          <select 
            value={promptType} 
            onChange={(e) => {
              setPromptType(e.target.value)
              if (e.target.value !== 'custom') {
                setShowCustomPrompt(false)
              }
            }}
            disabled={isRecording || isProcessing}
            className="prompt-select"
          >
            {promptTemplates.map(template => (
              <option key={template.id} value={template.id}>
                {template.name}
              </option>
            ))}
            <option value="custom">Custom Prompt</option>
          </select>
          
          {promptType !== 'custom' && (
            <button 
              onClick={() => setShowCustomPrompt(!showCustomPrompt)}
              disabled={isRecording || isProcessing}
              className="custom-prompt-toggle"
            >
              {showCustomPrompt ? 'Hide Custom' : 'Add Custom'}
            </button>
          )}
        </div>
        
        {promptTemplates.find(t => t.id === promptType) && (
          <p className="prompt-description">
            {promptTemplates.find(t => t.id === promptType).description}
          </p>
        )}
        
        {(showCustomPrompt || promptType === 'custom') && (
          <div className="custom-prompt-section">
            <textarea
              value={customPrompt}
              onChange={(e) => setCustomPrompt(e.target.value)}
              placeholder="Enter your custom prompt for analyzing the audio..."
              disabled={isRecording || isProcessing}
              className="custom-prompt-textarea"
              rows={4}
            />
          </div>
        )}
      </div>

      {/* Recording Controls */}
      <div className="recording-controls">
        <button 
          className={`record-button ${isRecording ? 'recording' : ''}`}
          onClick={handleRecordClick}
          disabled={isProcessing}
        >
          {isRecording ? '⏹️' : '🎤'}
        </button>
        
        <div className="status">
          {isProcessing ? (
            'Processing audio...'
          ) : isRecording ? (
            'Recording...'
          ) : (
            'Click to start recording'
          )}
        </div>
        
        {(isRecording || isProcessing) && (
          <div className="timer">
            {formatTime(recordingTime)}
          </div>
        )}
      </div>
    </div>
  )
}

export default VoiceRecorder