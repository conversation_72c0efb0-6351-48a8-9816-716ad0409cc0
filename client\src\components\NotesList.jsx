import React from 'react'

const NotesList = ({ notes, isLoading, onDeleteNote, onRefresh }) => {
  const formatDate = (isoString) => {
    const date = new Date(isoString)
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    })
  }

  const formatDuration = (seconds) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    if (mins > 0) {
      return `${mins}m ${secs}s`
    }
    return `${secs}s`
  }

  if (isLoading && notes.length === 0) {
    return (
      <div className="notes-section">
        <h2>Your Voice Notes</h2>
        <div className="loading-state">
          <p>Loading your notes...</p>
        </div>
      </div>
    )
  }

  if (notes.length === 0) {
    return (
      <div className="notes-section">
        <h2>Your Voice Notes</h2>
        <div className="empty-state">
          <p>No voice notes yet. Start recording to create your first note!</p>
        </div>
      </div>
    )
  }

  return (
    <div className="notes-section">
      <div className="notes-header">
        <h2>Your Voice Notes ({notes.length})</h2>
        <button onClick={onRefresh} className="refresh-button" disabled={isLoading}>
          {isLoading ? '🔄' : '↻'} Refresh
        </button>
      </div>

      {notes.map(note => (
        <div key={note.id} className="note-item">
          <div className="note-header">
            <div className="note-info">
              <span className="note-date">
                {formatDate(note.timestamp)}
              </span>
              {note.promptType && (
                <span className="note-prompt-type">
                  {note.promptType.charAt(0).toUpperCase() + note.promptType.slice(1)} Style
                </span>
              )}
              {note.serverId && (
                <span className="note-synced">
                  ☁️ Synced
                </span>
              )}
            </div>
            <div className="note-actions">
              <span className="note-duration">
                {formatDuration(note.duration)}
              </span>
              <button
                onClick={() => onDeleteNote(note.id)}
                className="delete-button"
                title="Delete note"
              >
                🗑️
              </button>
            </div>
          </div>

          <div className="note-summary">
            <h4>AI Summary</h4>
            <div className={note.error ? 'error' : ''}>
              {note.summary || 'Processing...'}
            </div>
          </div>

          {note.metadata && (
            <div className="note-metadata">
              <small>
                Model: {note.metadata.geminiModel || 'Unknown'} •
                Size: {note.metadata.audioSize ? `${Math.round(note.metadata.audioSize / 1024)}KB` : 'Unknown'}
                {note.metadata.processingTime && ` • Processed in ${note.metadata.processingTime}ms`}
              </small>
            </div>
          )}
        </div>
      ))}
    </div>
  )
}

export default NotesList