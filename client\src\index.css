* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background-color: #f5f5f5;
  color: #333;
  line-height: 1.6;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  text-align: center;
  margin-bottom: 40px;
}

.header h1 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.header p {
  color: #7f8c8d;
}

.error-banner {
  background-color: #fee;
  color: #c33;
  padding: 10px;
  border-radius: 6px;
  margin-top: 10px;
  border: 1px solid #fcc;
}

.recording-section {
  background: white;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.prompt-selection {
  margin-bottom: 30px;
  text-align: left;
}

.prompt-selection h3 {
  margin-bottom: 15px;
  color: #2c3e50;
  font-size: 18px;
}

.prompt-options {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-bottom: 10px;
}

.prompt-select {
  flex: 1;
  padding: 10px;
  border: 2px solid #ecf0f1;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  cursor: pointer;
}

.prompt-select:focus {
  outline: none;
  border-color: #3498db;
}

.custom-prompt-toggle {
  padding: 8px 16px;
  background: #f8f9fa;
  border: 2px solid #ecf0f1;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.custom-prompt-toggle:hover {
  background: #e9ecef;
  border-color: #3498db;
}

.prompt-description {
  color: #7f8c8d;
  font-size: 14px;
  font-style: italic;
  margin-bottom: 15px;
}

.custom-prompt-section {
  margin-top: 15px;
}

.custom-prompt-textarea {
  width: 100%;
  padding: 12px;
  border: 2px solid #ecf0f1;
  border-radius: 6px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  min-height: 100px;
}

.custom-prompt-textarea:focus {
  outline: none;
  border-color: #3498db;
}

.recording-controls {
  text-align: center;
}

.record-button {
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 50%;
  width: 80px;
  height: 80px;
  font-size: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 20px;
}

.record-button:hover {
  background: #c0392b;
  transform: scale(1.05);
}

.record-button.recording {
  background: #27ae60;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.status {
  font-size: 18px;
  margin-bottom: 10px;
}

.timer {
  font-size: 24px;
  font-weight: bold;
  color: #e74c3c;
}

.notes-section {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.notes-section h2 {
  margin-bottom: 20px;
  color: #2c3e50;
}

.note-item {
  border: 1px solid #ecf0f1;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  background: #fafafa;
}

.note-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.note-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.note-date {
  color: #7f8c8d;
  font-size: 14px;
}

.note-prompt-type {
  color: #3498db;
  font-size: 12px;
  font-weight: 500;
  background: #ecf8ff;
  padding: 2px 8px;
  border-radius: 12px;
  width: fit-content;
}

.note-duration {
  background: #3498db;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.note-summary {
  background: white;
  border-radius: 6px;
  padding: 15px;
  border-left: 4px solid #3498db;
}

.note-summary h4 {
  margin-bottom: 10px;
  color: #2c3e50;
}

.loading {
  color: #f39c12;
  font-style: italic;
}

.error {
  color: #e74c3c;
}

.empty-state {
  text-align: center;
  color: #7f8c8d;
  padding: 40px;
}

.empty-state p {
  font-size: 18px;
}

.loading-state {
  text-align: center;
  padding: 40px 20px;
  color: #7f8c8d;
}

.notes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.refresh-button {
  background: #3498db;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.refresh-button:hover:not(:disabled) {
  background: #2980b9;
}

.refresh-button:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}

.note-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.delete-button {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.delete-button:hover {
  background: #fee;
}

.note-synced {
  font-size: 12px;
  color: #27ae60;
  background: #d5f4e6;
  padding: 2px 6px;
  border-radius: 4px;
}

.note-metadata {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #ecf0f1;
}

.note-metadata small {
  color: #7f8c8d;
  font-size: 12px;
}