{"name": "vocable", "version": "1.0.0", "description": "Ambient voice notes with AI summaries", "type": "module", "scripts": {"dev": "concurrently \"npm run dev:client\" \"npm run dev:server\"", "dev:client": "cd client && npm run dev", "dev:server": "cd server && npm run dev", "build": "cd client && npm run build", "install:all": "npm install && cd client && npm install && cd ../server && npm install"}, "devDependencies": {"concurrently": "^8.2.2"}}